import { commaizeNumber } from "@toss/utils";
import { readUserCount } from "@workspace/db/crud/common/user";
import { Card } from "@workspace/ui/components/card";

const StatCard = ({
  title,
  subtitle,
  value,
  className = "",
}: {
  title: string;
  subtitle?: string;
  value: number | undefined;
  className?: string;
}) => (
  <Card className={`p-4 ${className}`}>
    <div className="flex items-center justify-between">
      <div className="text-sm text-gray-600">
        {title}
        {subtitle && (
          <div className="mt-1 text-xs text-gray-400">({subtitle})</div>
        )}
      </div>
      {/* <CircleHelp size={16} /> */}
    </div>
    <div className="mt-2 text-2xl font-bold">
      {value !== undefined ? commaizeNumber(value) : "-"}
    </div>
  </Card>
);

export const PointsStatsOverview = ({ tenantCd }: { tenantCd: string }) => {
  return (
    <div className="flex w-80 flex-col gap-4">
      {/* <UserStatsCount tenantCd={tenantCd} />
      <UserStatsCount tenantCd={tenantCd} /> */}
      <StatCard title="포인트 충전" subtitle="유상" value={undefined} />
      <StatCard title="포인트 적립" subtitle="무상" value={undefined} />
      <StatCard title="포인트 사용" subtitle="유상" value={undefined} />
      <StatCard title="포인트 사용" subtitle="무상" value={undefined} />
      <StatCard title="포인트 전환" subtitle="가져오기" value={undefined} />
      <StatCard title="포인트 전환" subtitle="내보내기" value={undefined} />
    </div>
  );
};
