import "server-only";
// 테이블 import
import {
  and,
  count,
  desc,
  eq,
  getTenantDb,
  like,
  or,
  sql,
} from "@workspace/db/drizzle";
import {
  pointDetails,
  pointEarnDetails,
  pointTransactions,
  users,
} from "@workspace/db/schema/common/points";
import { betweenDate, withPagination } from "@workspace/db/utils";
import { endOfMonth, startOfMonth } from "date-fns";
import { sum } from "drizzle-orm";

export type PointEarnDetail = typeof pointEarnDetails.$inferSelect;

export type PointSearchFilter = {
  searchType?: string;
  keyword?: string;
  gender?: string;
  vc?: string;
  vcDt?: [Date | undefined, Date | undefined];
  joinDt?: [Date | undefined, Date | undefined];
  lastLgnDt?: [Date | undefined, Date | undefined];
  userStts?: string;
  // 새로운 포인트 관련 필터 필드들
  serviceName?: string; // 서비스명 (전체/코나아이)
  pointType?: string; // 포인트 유형 (전체/공통/로컬/이벤트/기타포인트)
  pointDetailType?: string; // 포인트 상세 유형 (전체/코나아이 포인트 적립)
  serviceType?: string; // 서비스 유형 (전체/공통/지역특화)
  earnDt?: [Date | undefined, Date | undefined]; // 포인트 적립 일시
  expDt?: [Date | undefined, Date | undefined]; // 포인트 만료 기한
  pageSize?: number; // 조회건수
};

const createPointFilters = (filters: PointSearchFilter | undefined) => {
  const {
    searchType,
    keyword,
    gender,
    joinDt,
    lastLgnDt,
    userStts = "NORMAL",
    serviceName,
    pointType,
    earnDt,
    expDt,
  } = filters ?? {};

  const keywordFilter = keyword
    ? searchType === "all"
      ? or(
          like(users.userNm, `%${keyword}%`),
          like(users.telnoEnc, `%${keyword}%`),
          like(users.brdt, `%${keyword}%`),
        )
      : searchType === "userNm"
        ? like(users.userNm, `%${keyword}%`)
        : searchType === "phone"
          ? like(users.telnoEnc, `%${keyword}%`)
          : searchType === "birth"
            ? like(users.brdt, `%${keyword}%`)
            : undefined
    : undefined;

  return and(
    keywordFilter,
    userStts ? eq(users.userStts, userStts) : undefined,
    gender && gender !== "-" ? eq(users.gender, gender) : undefined,
    eq(pointTransactions.pntTxType, "포인트 적립"),
    joinDt ? betweenDate(users.joinDt, joinDt) : undefined,
    lastLgnDt ? betweenDate(users.lastLgnDt, lastLgnDt) : undefined,
    // 새로운 포인트 관련 필터들
    serviceName && serviceName !== "all"
      ? eq(pointTransactions.srvcCd, serviceName)
      : undefined,
    pointType && pointType !== "all"
      ? eq(pointTransactions.pntTypeCd, pointType)
      : undefined,
    earnDt ? betweenDate(pointTransactions.crtDt, earnDt) : undefined,
    expDt ? betweenDate(pointDetails.expDt, expDt) : undefined,
  );
};
export const readEarnPointCountMonthly = async (
  tenantCd: string,
  range: [Date, Date],
) => {
  const tenantDb = await getTenantDb(tenantCd);
  const result = await tenantDb
    .select({
      yearMonth: sql<string>`DATE_TRUNC('month', ${pointTransactions.crtDt})`,
      sum: sum(pointTransactions.pntAmt),
    })
    .from(pointTransactions)
    .where(
      and(
        betweenDate(pointTransactions.crtDt, range),
        eq(pointTransactions.pntTxType, "포인트 적립"),
      ),
    )
    .groupBy(sql`DATE_TRUNC('month', ${pointTransactions.crtDt})`)
    .orderBy(sql`DATE_TRUNC('month', ${pointTransactions.crtDt})`);

  return result.map(({ yearMonth, sum }) => ({
    yearMonth,
    sum: Number(sum ?? 0),
  }));
};

export const readEarnPointCountDaily = async (
  tenantCd: string,
  monthString: string,
): Promise<{ day: string; sum: number }[]> => {
  const tenantDb = await getTenantDb(tenantCd);
  const date = new Date(monthString);
  const fromDate = startOfMonth(date);
  const toDate = endOfMonth(date);

  const result = await tenantDb
    .select({
      day: sql<string>`DATE_TRUNC('day', ${pointTransactions.crtDt})`,
      sum: sum(pointTransactions.pntAmt),
    })
    .from(pointTransactions)
    .where(
      and(
        betweenDate(pointTransactions.crtDt, [fromDate, toDate]),
        eq(pointTransactions.pntTxType, "포인트 적립"),
      ),
    )
    .groupBy(sql`DATE_TRUNC('day', ${pointTransactions.crtDt})`)
    .orderBy(sql`DATE_TRUNC('day', ${pointTransactions.crtDt})`);

  return result.map(({ day, sum }) => ({
    day,
    sum: Number(sum ?? 0),
  }));
};

export const readPointCount = async (
  tenantCd: string,
  filters: PointSearchFilter = {},
) => {
  const tenantDb = await getTenantDb(tenantCd);

  const result = await tenantDb
    .select({ count: count() })
    .from(pointTransactions)
    .innerJoin(users, eq(pointTransactions.userDid, users.userDid))
    .innerJoin(
      pointDetails,
      eq(pointTransactions.pntTxId, pointDetails.pntTxId),
    )
    .where(createPointFilters(filters));

  return result[0]?.count ?? 0;
};

export const getPointHistory = async (
  tenantCd: string,
  filters: PointSearchFilter = {},
  page: number = 1,
  pageSize: number = 10,
): Promise<PointHistoryItem[]> => {
  const tenantDb = await getTenantDb(tenantCd);

  const query = tenantDb
    .select({
      userDid: pointTransactions.userDid,
      userNm: users.userNm,
      telnoEnc: users.telnoEnc,
      brdt: users.brdt,
      gender: users.gender,
      earnAmt: pointTransactions.pntAmt,
      remainAmt: pointTransactions.pointBlnc,
      earnDate: pointTransactions.crtDt,
      expDate: pointDetails.expDt,
      txRslt: pointTransactions.txRslt,
      // 추가 필드들
      srvcCd: pointTransactions.srvcCd,
      pntTypeCd: pointTransactions.pntTypeCd,
      pntTxType: pointTransactions.pntTxType,
      pntTxId: pointTransactions.pntTxId,
    })
    .from(pointTransactions)
    .innerJoin(users, eq(pointTransactions.userDid, users.userDid))
    .innerJoin(
      pointDetails,
      eq(pointTransactions.pntTxId, pointDetails.pntTxId),
    )
    .where(createPointFilters(filters))
    .orderBy(desc(pointTransactions.crtDt));

  const actualPageSize = filters.pageSize || pageSize;
  const result = await (page || actualPageSize
    ? withPagination(query.$dynamic(), page, actualPageSize)
    : query);

  return result.map((item, index) => ({
    userDid: item.userDid || "",
    userNm: item.userNm,
    telnoEnc: item.telnoEnc,
    brdt: item.brdt || "",
    gender: item.gender || "",
    earnAmt: Number(item.earnAmt || 0),
    remainAmt: Number(item.remainAmt || 0),
    earnDate: item.earnDate,
    expDate: item.expDate || new Date(),
    txRslt: item.txRslt || "",
    // 추가 필드들
    srvcCd: item.srvcCd || "",
    pntTypeCd: item.pntTypeCd || "",
    pntTxType: item.pntTxType || "",
    pntTxId: item.pntTxId || 0,
    rowNumber: (page - 1) * actualPageSize + index + 1,
  }));
};

export type PointHistoryItem = {
  userDid: string;
  userNm: string;
  telnoEnc: string;
  brdt: string;
  gender: string;
  earnAmt: number;
  remainAmt: number;
  earnDate: Date;
  expDate: Date;
  txRslt: string;
  // 추가 필드들
  srvcCd: string;
  pntTypeCd: string;
  pntTxType: string;
  pntTxId: number;
  rowNumber: number;
};
